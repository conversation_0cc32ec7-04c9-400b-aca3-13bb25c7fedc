#!/usr/bin/env node

// Test database connection
const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

async function testConnection() {
  console.log('🔍 Testing database connection...')
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Missing')
  
  const prisma = new PrismaClient({
    log: ['error', 'warn'],
  })

  try {
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful!')
    
    // Test a simple query
    const userCount = await prisma.user.count()
    console.log(`📊 Found ${userCount} users in database`)
    
    // Test if specific user exists
    const testUser = await prisma.user.findUnique({
      where: { account: '<EMAIL>' }
    })
    
    if (testUser) {
      console.log('👤 Test user found:', testUser.account)
      console.log('🔐 Password hash exists:', !!testUser.passwordHash)
    } else {
      console.log('❌ Test user not found in database')
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    return false
  } finally {
    await prisma.$disconnect()
  }
  
  return true
}

testConnection().catch(console.error)
