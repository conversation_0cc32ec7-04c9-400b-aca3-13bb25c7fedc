#!/usr/bin/env node

// Create test user for authentication testing
const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
require('dotenv').config({ path: '.env.local' })

async function createTestUser() {
  console.log('👤 Creating test user...')
  
  const prisma = new PrismaClient({
    log: ['error', 'warn'],
  })

  try {
    await prisma.$connect()
    console.log('✅ Connected to database')
    
    const email = '<EMAIL>'
    const password = 'William1!'
    const name = '<PERSON>'
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { account: email }
    })
    
    if (existingUser) {
      console.log('👤 User already exists, updating password...')
      
      // Update password hash
      const passwordHash = await bcrypt.hash(password, 10)
      
      await prisma.user.update({
        where: { account: email },
        data: {
          passwordHash,
          name
        }
      })
      
      console.log('✅ User password updated successfully')
    } else {
      console.log('👤 Creating new user...')
      
      // Hash the password
      const passwordHash = await bcrypt.hash(password, 10)
      
      // Create user
      const user = await prisma.user.create({
        data: {
          account: email,
          name,
          passwordHash
        }
      })
      
      console.log('✅ User created successfully:', user.account)
    }
    
    // Verify the user can be found and password works
    const user = await prisma.user.findUnique({
      where: { account: email }
    })
    
    if (user && user.passwordHash) {
      const isValidPassword = await bcrypt.compare(password, user.passwordHash)
      console.log('🔐 Password verification:', isValidPassword ? 'SUCCESS' : 'FAILED')
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser().catch(console.error)
