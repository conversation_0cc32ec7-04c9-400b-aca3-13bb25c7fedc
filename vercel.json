{"version": 2, "builds": [{"src": "next.config.js", "use": "@vercel/next"}], "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "regions": ["sin1"], "functions": {"src/app/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/storage/(.*)", "destination": "https://prj_oDXPr4Y1ZQEKXp8hwx5Y5LKYqhMI.supabase.co/storage/v1/object/public/$1"}], "env": {"NODE_ENV": "production"}}