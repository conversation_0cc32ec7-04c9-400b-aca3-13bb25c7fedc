@import 'tailwindcss';

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 14, 17, 23;
  --background-end-rgb: 14, 17, 23;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb)))
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .target-dimensions {
    width: 116px;
    height: 176px;
    object-fit: cover;
  }

  .video-dimensions {
    width: 116px;
    height: 176px;
    object-fit: cover;
  }

  .source-dimensions {
    @apply w-full h-full object-cover aspect-square;
  }

  .material {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .item-box {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 16/9;
  }

  .duration {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
  }

  .author {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
  }

  /* Shimmer animation for skeletons */
  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(to right, #2a2d34 8%, #3a3d44 18%, #2a2d34 33%);
    background-size: 200% 100%;
  }
}
