.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 0 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.card {
  background: white;
  border-radius: 1rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2.5rem;
  width: 100%;
  max-width: 420px;
}

.header {
  margin-bottom: 2.5rem;
  text-align: center;
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logoText {
  font-size: 1.5rem;
  font-weight: 700;
  margin-left: 0.5rem;
  background-image: linear-gradient(to right, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.75rem;
  letter-spacing: -0.025em;
}

.subtitle {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.5;
}

.footer {
  margin-top: 2.5rem;
  text-align: center;
}

.terms {
  font-size: 0.8125rem;
  color: #64748b;
  line-height: 1.5;
}

.link {
  color: #3b82f6;
  text-decoration: underline;
  font-weight: 500;
}

.providers {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  width: 100%;
}

.divider {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: #e2e8f0;
}

.logo {
  margin-bottom: 1.5rem;
  width: 48px;
  height: 48px;
}

.emailButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  background-color: #f8fafc;
  color: #1e293b;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  cursor: pointer;
}

.emailButton:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.backLinkContainer {
  margin-top: 1.5rem;
  text-align: center;
}

.backLink {
  color: #64748b;
  font-size: 0.875rem;
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.backLink:hover {
  color: #334155;
  text-decoration: underline;
}
