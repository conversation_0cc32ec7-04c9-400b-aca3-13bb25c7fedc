.formContainer {
  margin-top: 1.5rem;
  width: 100%;
}

.formTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form {
  width: 100%;
}

.formGroup {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  color: #4b5563;
  font-weight: 500;
  display: inline-block;
}

/* Visually hidden label class for screen readers */
.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  background-color: #f8fafc;
  transition:
    border-color 0.2s ease,
    box-shadow 0.2s ease;
}

.input:hover {
  border-color: #cbd5e1;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.input[aria-invalid='true'] {
  border-color: #ef4444;
}

.input[aria-invalid='true']:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: background-color 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.button:hover {
  background-color: #2563eb;
}

.button:focus {
  outline: none;
}

.button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  text-align: center;
}

.switchMode {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: #64748b;
}

.switchButton {
  color: #3b82f6;
  font-weight: 500;
  margin-left: 0.25rem;
  background: none;
  border: none;
  cursor: pointer;
}

.switchButton:hover {
  text-decoration: underline;
}

.spinnerContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.forgotPassword {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.forgotLink {
  color: #3b82f6;
  font-size: 0.875rem;
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition:
    color 0.2s ease,
    background-color 0.2s ease;
}

.forgotLink:hover {
  text-decoration: underline;
  color: #2563eb;
}

.forgotLink:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  background-color: rgba(59, 130, 246, 0.05);
}
