ALTER TABLE public."User" DISABLE TRIGGER ALL;
INSERT INTO public."User" (id, account, name, password_hash, last_login, last_logout) VALUES ('********-aa84-46db-997f-f7789d28d5f6', '<EMAIL>', '<PERSON>', '$2b$10$34aW45OCjvUzn9qTW2GzUeILsooYaMXhLVu964xurxmy/yHJsn3.6', '2025-05-29 14:45:26.231', NULL);
INSERT INTO public."User" (id, account, name, password_hash, last_login, last_logout) VALUES ('34a23157-9f9a-4334-b3d8-69ae71344f01', '<EMAIL>', '<PERSON>', '$2b$10$1VgZI6/ww.036jOr8m1Eiuw7350azKwvvWEiKyyFOKApdRSjJ37vi', '2025-05-29 15:27:35.315', NULL);
INSERT INTO public."User" (id, account, name, password_hash, last_login, last_logout) VALUES ('8e7ba467-4b2a-4ffa-90c8-7af5271b121f', '<EMAIL>', 'william jiang', NULL, '2025-06-30 08:26:22.562', NULL);
INSERT INTO public."User" (id, account, name, password_hash, last_login, last_logout) VALUES ('********-b6ee-482b-b9da-7e74966f1fe5', '<EMAIL>', NULL, NULL, '2025-05-27 03:15:04.08', NULL);
INSERT INTO public."User" (id, account, name, password_hash, last_login, last_logout) VALUES ('6bb8415c-81d6-45c5-9164-e7b4b76bee54', '<EMAIL>', 'William Jiang', NULL, '2025-06-21 12:12:04.247', NULL);
ALTER TABLE public."User" ENABLE TRIGGER ALL;
ALTER TABLE public."FaceSource" DISABLE TRIGGER ALL;
INSERT INTO public."FaceSource" (id, filename, width, height, file_path, file_size, mime_type, created_at, last_used_at, usage_count, is_active, author_id) VALUES ('ebc3b796-8093-47c9-adaf-5250214f21c0', '1748312978896_image.png', 800, 1024, '/sources/1748312978896_image.png', 852054, 'image/png', '2025-05-27 02:29:38.904', NULL, 0, false, '********-b6ee-482b-b9da-7e74966f1fe5');
INSERT INTO public."FaceSource" (id, filename, width, height, file_path, file_size, mime_type, created_at, last_used_at, usage_count, is_active, author_id) VALUES ('a326e16e-3acc-4347-83d2-ef71b4640a87', '1748314438931_image.png', 800, 1024, '/sources/1748314438931_image.png', 852054, 'image/png', '2025-05-27 02:53:58.936', NULL, 0, false, '********-b6ee-482b-b9da-7e74966f1fe5');
INSERT INTO public."FaceSource" (id, filename, width, height, file_path, file_size, mime_type, created_at, last_used_at, usage_count, is_active, author_id) VALUES ('c61cbd19-fc37-4427-8eff-cda34ddd06ae', '1748315092501_image.png', 800, 1024, '/sources/1748315092501_image.png', 852054, 'image/png', '2025-05-27 03:04:52.508', NULL, 0, false, '********-b6ee-482b-b9da-7e74966f1fe5');
INSERT INTO public."FaceSource" (id, filename, width, height, file_path, file_size, mime_type, created_at, last_used_at, usage_count, is_active, author_id) VALUES ('62fb2687-7bd2-4938-9a3e-b77b88cf01d8', '1748315755125_image.png', 800, 1024, '/sources/1748315755125_image.png', 852054, 'image/png', '2025-05-27 03:15:55.131', NULL, 0, true, '8e7ba467-4b2a-4ffa-90c8-7af5271b121f');
ALTER TABLE public."FaceSource" ENABLE TRIGGER ALL;
ALTER TABLE public."TargetTemplate" DISABLE TRIGGER ALL;
INSERT INTO public."TargetTemplate" (id, filename, type, file_path, thumbnail_path, file_size, duration, mime_type, usage_count, created_at, last_used_at, is_active, author_id) VALUES ('c2f32406-796d-44a9-a62d-56f094824af1', 'video.mp4', 'video', '/videos/video.mp4', '/thumbnails/video_thumbnail.webp', 403621, 5, 'video/mp4', 0, '2025-05-27 03:15:48.266', NULL, true, '8e7ba467-4b2a-4ffa-90c8-7af5271b121f');
ALTER TABLE public."TargetTemplate" ENABLE TRIGGER ALL;
ALTER TABLE public."GeneratedMedia" DISABLE TRIGGER ALL;
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('a1a05229-c22f-495b-b83c-8ebd2ddf4bc1', '1.mp4', 'video', NULL, '/outputs/1.mp4', 7850262, 'video/mp4', '2025-05-25 03:01:30.264', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('18a31c8f-b665-43f3-993a-a14d8c221fba', '10.mp4', 'video', NULL, '/outputs/10.mp4', 25414614, 'video/mp4', '2025-05-25 03:01:30.377', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('dab2e330-45e9-4464-b7e3-b250b5e35802', '2.mp4', 'video', NULL, '/outputs/2.mp4', 17567685, 'video/mp4', '2025-05-25 03:01:30.481', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('4ec3fce3-9345-4ff8-bf2f-eb40fc99d35a', '6.mp4', 'video', NULL, '/outputs/6.mp4', 17567017, 'video/mp4', '2025-05-25 03:01:30.911', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('e856c398-6860-4adf-8edd-d39f4d820df6', '7.mp4', 'video', NULL, '/outputs/7.mp4', 17574343, 'video/mp4', '2025-05-25 03:01:31.016', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('41b61c9b-d632-4dd9-a36c-618bc1a3f212', '9.mp4', 'video', NULL, '/outputs/9.mp4', 7839762, 'video/mp4', '2025-05-25 03:01:31.23', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('990f21d1-036e-4b66-aae8-e3b29e35274d', 'margot_1.mp4', 'video', NULL, '/outputs/margot_1.mp4', 7850781, 'video/mp4', '2025-05-25 03:01:31.334', 0, false, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('23e7cd0d-50f2-4fcb-87c4-06b9f3ccf565', '1748315755125_image_1748527789184.mp4', 'video', '/tmp/tmpwpdwe03h/c37e0adeea4a4d41a9ee722d7e483812.mp4', '/outputs/1748315755125_image_1748527789184.mp4', 702137, 'video/mp4', '2025-05-29 14:10:48.471', 0, false, true, '********-aa84-46db-997f-f7789d28d5f6', NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('f289700a-3ee9-4630-9f1a-038a04e150ce', '3.mp4', 'video', NULL, '/outputs/3.mp4', 7857755, 'video/mp4', '2025-05-25 03:01:30.589', 0, true, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('fe4a625d-e108-423d-8f46-4dd758f8767c', '8.mp4', 'video', NULL, '/outputs/8.mp4', 26513247, 'video/mp4', '2025-05-25 03:01:31.124', 0, true, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('90faf1a3-fa87-40d5-ac22-a7e24ec1cf2f', '4.mp4', 'video', NULL, '/outputs/4.mp4', 6397599, 'video/mp4', '2025-05-25 03:01:30.694', 0, true, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('b6898bc3-d932-4b5e-9da5-6a943f7fe296', '5.mp4', 'video', NULL, '/outputs/5.mp4', 26490684, 'video/mp4', '2025-05-25 03:01:30.805', 0, true, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('67ed1ecc-b886-41af-95b1-d0dd0860f860', 'margot_2.mp4', 'video', NULL, '/outputs/margot_2.mp4', 6410560, 'video/mp4', '2025-05-25 03:01:31.442', 1, true, true, NULL, NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('118c0fd9-de2a-4a2d-a790-e84a364900a2', '1748315755125_image_1748531084061.mp4', 'video', '/tmp/tmp0odd8nql/cb19825f15604c4daedc4216bd45ef4f.mp4', '/outputs/1748315755125_image_1748531084061.mp4', 702137, 'video/mp4', '2025-05-29 15:04:46.756', 3, true, true, '34a23157-9f9a-4334-b3d8-69ae71344f01', NULL, NULL);
INSERT INTO public."GeneratedMedia" (id, name, type, temp_path, file_path, file_size, mime_type, created_at, download_count, "isPaid", is_active, author_id, template_id, face_source_id) VALUES ('48c20f40-6121-4779-ad2a-b753e2784af6', 'margot_3.mp4', 'video', NULL, '/outputs/margot_3.mp4', 26578709, 'video/mp4', '2025-05-25 03:01:31.552', 0, true, true, NULL, NULL, NULL);
ALTER TABLE public."GeneratedMedia" ENABLE TRIGGER ALL;
ALTER TABLE public."Guideline" DISABLE TRIGGER ALL;
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('8ce6cafa-2315-450f-91b7-26b02c881675', 'f1.png', 140, 140, 'image/png', 35421, '/guidelines/f1.png', false, '2025-05-25 03:01:21.157', '2025-05-25 03:01:21.157');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('b85dc9b6-5be8-49d7-832e-e0de12e6c081', 'f2.png', 140, 140, 'image/png', 37444, '/guidelines/f2.png', false, '2025-05-25 03:01:21.237', '2025-05-25 03:01:21.237');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('e04c0d3d-a889-4ef6-9f84-b9e830824882', 'f3.png', 140, 140, 'image/png', 25323, '/guidelines/f3.png', false, '2025-05-25 03:01:21.316', '2025-05-25 03:01:21.316');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('ffad3cfd-4535-4833-9b43-81085a1a0f0a', 'f4.png', 140, 140, 'image/png', 20241, '/guidelines/f4.png', false, '2025-05-25 03:01:21.392', '2025-05-25 03:01:21.392');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('4b03f7b1-21d8-4fe6-81fd-d4e94f614236', 's1.png', 140, 140, 'image/png', 30969, '/guidelines/s1.png', true, '2025-05-25 03:01:21.467', '2025-05-25 03:01:21.467');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('ca9ed4de-7385-4a13-8353-5b15a4e6698c', 's2.png', 140, 140, 'image/png', 34414, '/guidelines/s2.png', true, '2025-05-25 03:01:21.54', '2025-05-25 03:01:21.54');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('0aae146f-3e71-454f-aec5-10588746a647', 's3.png', 140, 140, 'image/png', 33424, '/guidelines/s3.png', true, '2025-05-25 03:01:21.617', '2025-05-25 03:01:21.617');
INSERT INTO public."Guideline" (id, filename, width, height, file_type, file_size, file_path, is_allowed, created_at, updated_at) VALUES ('e3bac802-691a-4bf1-9d1f-c7a969b2d4dd', 's4.png', 140, 140, 'image/png', 32757, '/guidelines/s4.png', true, '2025-05-25 03:01:21.692', '2025-05-25 03:01:21.692');
ALTER TABLE public."Guideline" ENABLE TRIGGER ALL;
ALTER TABLE public."Payment" DISABLE TRIGGER ALL;
INSERT INTO public."Payment" (id, amount, currency, status, type, "txHash", "createdAt", "userId", "generatedMediaId") VALUES ('701a797f-74cf-4fb8-a954-4772ce91c9b8', 4.980000000000000000000000000000, 'USD', 'completed', 'fiat', NULL, '2025-06-21 04:41:20.179', '6bb8415c-81d6-45c5-9164-e7b4b76bee54', 'f289700a-3ee9-4630-9f1a-038a04e150ce');
INSERT INTO public."Payment" (id, amount, currency, status, type, "txHash", "createdAt", "userId", "generatedMediaId") VALUES ('ccf0f302-d1bb-4b3c-882d-c08e21c8f688', 4.980000000000000000000000000000, 'USD', 'completed', 'fiat', NULL, '2025-06-21 04:52:33.604', '6bb8415c-81d6-45c5-9164-e7b4b76bee54', '118c0fd9-de2a-4a2d-a790-e84a364900a2');
INSERT INTO public."Payment" (id, amount, currency, status, type, "txHash", "createdAt", "userId", "generatedMediaId") VALUES ('f5e18f5a-e372-4f03-97fd-0e9a61fe011a', 4.980000000000000000000000000000, 'USD', 'completed', 'fiat', NULL, '2025-06-21 06:07:21.651', '6bb8415c-81d6-45c5-9164-e7b4b76bee54', 'fe4a625d-e108-423d-8f46-4dd758f8767c');
INSERT INTO public."Payment" (id, amount, currency, status, type, "txHash", "createdAt", "userId", "generatedMediaId") VALUES ('f155cbb2-b9dc-4efd-a329-f137e670d7b2', 4.980000000000000000000000000000, 'USD', 'completed', 'fiat', NULL, '2025-06-21 06:20:56.39', '6bb8415c-81d6-45c5-9164-e7b4b76bee54', '90faf1a3-fa87-40d5-ac22-a7e24ec1cf2f');
INSERT INTO public."Payment" (id, amount, currency, status, type, "txHash", "createdAt", "userId", "generatedMediaId") VALUES ('e9b94c91-a3c4-4d3b-9df6-ff1690aeea61', 4.960000000000000000000000000000, 'USD', 'completed', 'fiat', NULL, '2025-06-21 09:58:41.388', '6bb8415c-81d6-45c5-9164-e7b4b76bee54', 'b6898bc3-d932-4b5e-9da5-6a943f7fe296');
INSERT INTO public."Payment" (id, amount, currency, status, type, "txHash", "createdAt", "userId", "generatedMediaId") VALUES ('b0eecdbf-9330-43ba-b0a1-5b56a7334b0e', 4.960000000000000000000000000000, 'USD', 'completed', 'fiat', NULL, '2025-06-23 09:42:07.658', '8e7ba467-4b2a-4ffa-90c8-7af5271b121f', '48c20f40-6121-4779-ad2a-b753e2784af6');
ALTER TABLE public."Payment" ENABLE TRIGGER ALL;
ALTER TABLE public._prisma_migrations DISABLE TRIGGER ALL;
ALTER TABLE public._prisma_migrations ENABLE TRIGGER ALL;
