{"name": "face-swap-poc", "version": "0.5.6", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:cloud": "next dev --turbopack", "dev:local": "supabase start && next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "postinstall": "prisma generate", "lint": "next lint", "format": "prettier --write .", "check-format": "prettier --check .", "lint-fix": "eslint --fix --ext .js,.jsx,.ts,.tsx .", "lint-src": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:seed": "prisma db seed", "setup": "npm install && prisma generate", "setup:cloud": "npm run setup && echo 'Using cloud Supabase - ready to start development'", "setup:local": "npm run setup && supabase start && echo 'Local Supabase started - ready for development'"}, "dependencies": {"@headlessui/react": "^2.2.0", "@prisma/client": "^6.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.8", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "dotenv": "^17.0.0", "form-data": "^4.0.2", "lucide-react": "^0.507.0", "next": "15.2.4", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "pg": "^8.15.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sharp": "^0.34.2", "stripe": "^18.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.5", "@types/react": "19.1.3", "dependency-cruiser": "^16.10.2", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-unused-imports": "^4.1.4", "npm-check": "^6.0.1", "prettier": "^3.5.3", "prisma": "^6.7.0", "tailwindcss": "^4.1.5"}}