# Vercel Environment Variables Setup

## Required Environment Variables for Production

Go to your Vercel Dashboard → Project Settings → Environment Variables and set these:

### Database & Authentication
```bash
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
NEXTAUTH_URL="https://face-fusion-agent.vercel.app"
NEXTAUTH_SECRET="JJ3X347hNuk/l+zHwUc/H+1dZLuxoDNdbgumlZLZXAY="
```

### Supabase Configuration
```bash
NEXT_PUBLIC_SUPABASE_URL="https://yunxidsqumhfushjcgyg.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.r8cKAiTxF1XKLUXtWbRfnEHj_qZCZoNnyFSlcstVZu0"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.mISuwZ9Xt_8PwZXOI-pAK8CDXvgRdZgGpXgkxeu33Mo"
```

### OAuth Providers
```bash
GOOGLE_CLIENT_ID="223496341184-nchc8odtsrds2hsn3s6ipraf2pnek14v.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
AZURE_AD_CLIENT_ID="473167b8-889a-4225-ae87-b273b0088af7"
AZURE_AD_CLIENT_SECRET="****************************************"
AZURE_AD_TENANT_ID="7f411edf-93b6-48cd-8a13-7f925390d14f"
```

### Payment & External APIs
```bash
STRIPE_SECRET_KEY="sk_test_51R78xYPQv7UPATi3Jz01ypOqm9SEEKHLrXvbflT0TPlPEbM0HjlX4pf6AGP4WBQAG2Mb3c2bp7ReTKu9KUXeURoV00avomtvjC"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51R78xYPQv7UPATi31Rio529K34yYhMA7ax0FmEJCNEtxxJURKjBs3zOHqwWk0GL6JENruYUO4imro4cenYydxhN400Cp9hrMPI"
STRIPE_WEBHOOK_SECRET="whsec_vt16OsnGfJtLfCwdEZ1ZHyuX7sbQP6uE"
MODAL_QUERY_API="https://ultimatech-research--facefusion-agent-facefusionagent-do-29747a.modal.run"
MODAL_CREATE_API="https://ultimatech-research--facefusion-agent-facefusionagent-index.modal.run"
```

### Application URLs
```bash
NEXT_PUBLIC_BASE_URL="https://face-fusion-agent.vercel.app"
NEXT_PUBLIC_APP_URL="https://face-fusion-agent.vercel.app"
```

## Important Notes:

1. **Password Encoding**: The `!` characters in the password are URL-encoded as `%21`
2. **Environment Scope**: Set all variables for "Production" environment
3. **Redeploy**: After setting variables, redeploy the application
4. **OAuth Redirects**: Make sure Google/Azure OAuth redirect URIs include your Vercel domain

## OAuth Redirect URIs to Add:

### Google Console:
- `https://face-fusion-agent.vercel.app/api/auth/callback/google`

### Azure Portal:
- `https://face-fusion-agent.vercel.app/api/auth/callback/azure-ad`
